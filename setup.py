from setuptools import setup, find_packages

setup(
  name = 'toolformer-pytorch',
  packages = find_packages(exclude=[]),
  version = '0.0.30',
  license='MIT',
  description = 'Toolformer - Pytorch',
  author = '<PERSON>',
  author_email = '<EMAIL>',
  long_description_content_type = 'text/markdown',
  url = 'https://github.com/lucidrains/toolformer-pytorch',
  keywords = [
    'artificial intelligence',
    'deep learning',
    'transformers',
    'attention mechanism',
    'automated-tool-use'
  ],
  install_requires=[
    'beartype',
    'einops>=0.4',
    'torch>=1.6',
    'tqdm',
    'x-clip>=0.14.3'
  ],
  classifiers=[
    'Development Status :: 4 - Beta',
    'Intended Audience :: Developers',
    'Topic :: Scientific/Engineering :: Artificial Intelligence',
    'License :: OSI Approved :: MIT License',
    'Programming Language :: Python :: 3.6',
  ],
)
